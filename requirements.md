项目类型：创建一个全新项目。
项目概述：使用 C++ 在 macOS 上实现一个极致高效的 IPv4 UDP 端口转发程序。程序通过命令行接受任意数量的参数，每个参数以空格分隔，格式为“接收源IP:接收源端口,接收目的IP:接收目的端口,转发源IP:转发源端口,转发目的IP:转发目的端口”。启动时解析并严格校验所有参数的语法与数值合法性。程序必须利用 macOS 原生的 kqueue、SO_REUSEPORT、零拷贝等技术，实现双向 UDP 转发：① 从“接收源IP:接收源端口”接收数据，经由“接收目的IP:接收目的端口”转发至对应的“转发源IP:转发源端口”，再从该端口发送到“转发目的IP:转发目的端口”；② 同时将从“转发目的IP:转发目的端口”返回的 UDP 包，通过“转发源IP:转发源端口”接收后，使用“接收目的IP:接收目的端口”转发回原始的“接收源IP:接收源端口”。要求：1）支持高并发、低延迟，使用非阻塞套接字和多线程/线程池或基于 io_uring（若可用）实现；2）提供详细的运行时日志，包括参数解析、绑定成功、转发统计、错误信息；3）实现优雅的信号处理（SIGINT、SIGTERM）以安全关闭所有 socket；4）代码必须符合 C++20 标准，使用 RAII 管理资源，避免内存泄漏；5）提供 CMake 构建脚本，支持 Release 与 Debug 两种模式，并在 Release 中开启最高级别的编译优化（-O3、-march=native）。请给出完整的项目结构说明、关键实现思路以及示例命令行调用方式。